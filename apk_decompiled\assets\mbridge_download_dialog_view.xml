<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/mbridge_relativeall"
    android:background="@drawable/mbridge_downlod_diaolog_background"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:id="@+id/mbridge_apptitle"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <Button
            android:id="@+id/mbridge_buttonbottom"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:layout_marginLeft="70dp"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp"
            android:layout_marginRight="70dp"
            android:gravity="center"
            android:contentDescription="download"
            android:text="立即下载"
            android:textColor="#ffffff"
            android:letterSpacing="0.2"
            android:textSize="20sp"/>
        <RelativeLayout
            android:id="@+id/mbridge_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true">
            <TextView
                android:id="@+id/mbridge_textView3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:text="应用详情"
                android:gravity="center"
                android:textColor="#000000"
                android:textSize="16sp"
                android:textStyle="bold"/>

            <RelativeLayout android:layout_width="70dp"
                android:layout_height="70dp"
                android:layout_alignParentRight="true"
                android:layout_centerVertical="true"
                android:contentDescription="close"
                android:layout_marginRight="10dp">
                <ImageView
                    android:layout_centerVertical="true"
                    android:id="@+id/mbridge_imageView2"
                    android:layout_width="15dp"
                    android:layout_centerHorizontal="true"
                    android:layout_height="15dp"
                    android:src="@drawable/close"
                    android:contentDescription="close"/>
            </RelativeLayout>
        </RelativeLayout>
        <ScrollView
            android:id="@+id/mbridge_scollcontent"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@id/mbridge_buttonbottom"
            android:layout_below="@id/mbridge_title"
            android:layout_marginLeft="5dp"
            android:layout_marginRight="5dp"
            android:layout_marginTop="5dp">
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <RelativeLayout
                    android:layout_marginLeft="15dp"
                    android:layout_marginRight="15dp"
                    android:id="@+id/mbridge_titlecontent"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <com.mbridge.msdk.widget.custom.baseview.MBRoundRectImageViewC
                        android:id="@+id/mbridge_imageView6"
                        android:layout_width="60dp"
                        android:layout_height="60dp"
                        android:layout_centerVertical="true"
                        android:layout_alignParentLeft="true"
                        android:contentDescription="getApplicationLogo"/>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_centerVertical="true"
                        android:layout_marginLeft="10dp"
                        android:layout_alignParentTop="true"
                        android:layout_toRightOf="@id/mbridge_imageView6"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/mbridge_textView4"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:contentDescription="getApplicationNameFromCampaign"
                            android:singleLine="true"
                            android:ellipsize="true"
                            android:textColor="#000000"
                            android:gravity="top"
                            android:textSize="15sp"
                            android:textStyle="bold"/>

                        <TextView
                            android:id="@+id/mbridge_textView5"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:textSize="12sp"
                            android:singleLine="true"
                            android:ellipsize="true"
                            android:contentDescription="getApplicationContext"/>

                        <RelativeLayout
                            android:id="@+id/mbridge_ring"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_marginTop="2dp">

                            <com.mbridge.msdk.widget.custom.baseview.MBStarLevelLayoutView
                                android:id="@+id/mbridge_xingxing"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_alignParentLeft="true"
                                android:layout_centerVertical="true"
                                android:contentDescription="getStarCount"
                            ></com.mbridge.msdk.widget.custom.baseview.MBStarLevelLayoutView>

                            <TextView
                                android:id="@+id/mbridge_textView6"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textSize="10sp"
                                android:layout_marginLeft="10dp"
                                android:layout_toRightOf="@id/mbridge_xingxing"
                                android:layout_centerVertical="true"
                                android:contentDescription="getApplicationScore"/>
                        </RelativeLayout>
                    </LinearLayout>
                </RelativeLayout>


                <RelativeLayout
                    android:layout_marginLeft="15dp"
                    android:layout_marginRight="15dp"
                    android:id="@+id/mbridge_imageframe"
                    android:layout_below="@id/mbridge_titlecontent"
                    android:layout_width="match_parent"
                    android:contentDescription="bigpicsize"
                    android:layout_marginTop="10dp"
                    android:layout_height="wrap_content">>

                    <com.mbridge.msdk.widget.custom.baseview.MBRoundRectImageViewC
                        android:id="@+id/mbridge_imageView22"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:layout_centerVertical="true"
                        android:layout_alignParentLeft="true"
                        android:layout_alignParentRight="true"
                        android:contentDescription="imgbg"/>

                    <ImageView
                        android:id="@+id/mbridge_imageView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:layout_centerVertical="true"
                        android:scaleType="center"
                        android:contentDescription="getApplicationImage"/>
                </RelativeLayout>

                <LinearLayout
                    android:layout_marginLeft="15dp"
                    android:layout_marginRight="15dp"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_below="@id/mbridge_imageframe"
                    android:orientation="vertical">

                    <RelativeLayout
                        android:id="@+id/mbridge_appanme"
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:contentDescription="getApplicationName#1"
                        android:layout_alignParentTop="true">

                        <TextView
                            android:id="@+id/mbridge_textname"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentLeft="true"
                            android:layout_centerVertical="true"
                            android:textColor="#999999"
                            android:contentDescription="getApplicationName"
                            android:textSize="12sp"
                            android:text="应用名称"/>

                        <TextView
                            android:id="@+id/mbridge_contentname"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:textSize="12sp"
                            android:layout_toRightOf="@id/mbridge_textname"
                            android:gravity="right"
                            android:ellipsize="true"
                            android:textColor="#808080"
                            android:layout_marginLeft="30dp"
                            android:layout_centerVertical="true"
                            android:contentDescription="getApplicationName"
                            android:text=""
                            app:srcCompat="@android:drawable/ic_menu_close_clear_cancel"/>
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_alignParentBottom="true"
                            android:background="#E5E5E5"/>
                    </RelativeLayout>


                    <RelativeLayout
                        android:id="@+id/mbridge_appversion"
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:layout_alignParentTop="true"
                        android:contentDescription="getApplicationVersion#1">

                        <TextView
                            android:id="@+id/mbridge_versionname"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentLeft="true"
                            android:layout_centerVertical="true"
                            android:contentDescription="getApplicationVersion"
                            android:textSize="12sp"
                            android:textColor="#999999"
                            android:text="版本"/>

                        <TextView
                            android:id="@+id/mbridge_versionrightname"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:textSize="12sp"
                            android:layout_toRightOf="@id/mbridge_versionname"
                            android:gravity="right"
                            android:layout_marginLeft="30dp"
                            android:ellipsize="true"
                            android:layout_centerVertical="true"
                            android:contentDescription="getApplicationVersion"
                            android:text=""
                            android:textColor="#808080"
                            app:srcCompat="@android:drawable/ic_menu_close_clear_cancel"/>
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_alignParentBottom="true"
                            android:background="#E5E5E5"/>
                    </RelativeLayout>


                    <RelativeLayout
                        android:id="@+id/mbridge_appdevelo1"
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:contentDescription="getApplicationDeveloper#1"
                        android:layout_alignParentTop="true">

                        <TextView
                            android:id="@+id/mbridge_developname"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentLeft="true"
                            android:contentDescription="getApplicationDeveloper"
                            android:layout_centerVertical="true"
                            android:textSize="12sp"
                            android:textColor="#999999"
                            android:text="开发者"/>

                        <TextView
                            android:id="@+id/mbridge_developrightname"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:gravity="right"
                            android:textSize="12sp"
                            android:textColor="#808080"
                            android:ellipsize="true"
                            android:layout_marginLeft="30dp"
                            android:layout_toRightOf="@id/mbridge_developname"
                            android:contentDescription="getApplicationDeveloper"
                            android:text=""
                            app:srcCompat="@android:drawable/ic_menu_close_clear_cancel"/>
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_alignParentBottom="true"
                            android:background="#E5E5E5"/>
                    </RelativeLayout>


                    <RelativeLayout
                        android:id="@+id/mbridge_appdevelo"
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:contentDescription="getApplicationUpdateTime#1"
                        android:layout_alignParentTop="true">

                        <TextView
                            android:id="@+id/mbridge_developname1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentLeft="true"
                            android:layout_centerVertical="true"
                            android:textSize="12sp"
                            android:textColor="#999999"
                            android:contentDescription="getApplicationUpdateTime"
                            android:text="更新时间"/>

                        <TextView
                            android:id="@+id/mbridge_developrightname1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:ellipsize="true"
                            android:gravity="right"
                            android:textSize="12sp"
                            android:layout_marginLeft="30dp"
                            android:textColor="#808080"
                            android:layout_toRightOf="@id/mbridge_developname1"
                            android:contentDescription="getApplicationUpdateTime"
                            app:srcCompat="@android:drawable/ic_menu_close_clear_cancel"/>
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:layout_alignParentBottom="true"
                            android:background="#E5E5E5"/>
                    </RelativeLayout>


                    <RelativeLayout
                        android:id="@+id/mbridge_yinsidevelo"
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:contentDescription="getApplicationPrivacy#1"
                        android:layout_alignParentTop="true">

                        <TextView
                            android:id="@+id/mbridge_ysname"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentLeft="true"
                            android:layout_centerVertical="true"
                            android:textSize="12sp"
                            android:textColor="#999999"
                            android:contentDescription="getApplicationPrivacy"
                            android:text="隐私权限"/>

                        <ImageView
                            android:id="@+id/mbridge_ysdeveloprightname"
                            android:layout_width="15dp"
                            android:layout_height="15dp"
                            android:gravity="right"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:contentDescription="getApplicationPrivacy"
                        />
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:contentDescription="getApplicationPrivacy"
                            android:layout_alignParentBottom="true"
                            android:background="#E5E5E5"/>
                    </RelativeLayout>


                    <RelativeLayout
                        android:id="@+id/mbridge_yingyongdevelo"
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:layout_alignParentTop="true"
                        android:contentDescription="apppersionexp#2"
                        android:orientation="vertical">

                        <TextView
                            android:id="@+id/mbridge_yingyongname"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentLeft="true"
                            android:layout_centerVertical="true"
                            android:contentDescription="txquanxian"
                            android:textSize="12sp"
                            android:textColor="#999999"
                            android:text="应用权限"/>

                        <ImageView
                            android:id="@+id/mbridge_yingyongdevelodeveloprightname"
                            android:layout_width="15dp"
                            android:layout_height="15dp"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:contentDescription="permissionexpand"
                        />
                    </RelativeLayout>


                    <LinearLayout
                        android:id="@+id/mbridge_linearquanxian"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:visibility="gone"
                        android:contentDescription="apppersion"
                        android:orientation="vertical">
                        <TextView
                            android:id="@+id/mbridge_quanxian1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="12sp"
                            android:textColor="#808080"
                            android:ellipsize="true"
                            android:contentDescription="getApplicationPermissions"/>
                    </LinearLayout>
                    <RelativeLayout
                        android:id="@+id/mbridge_app_desc_rl"
                        android:layout_width="match_parent"
                        android:layout_height="40dp"
                        android:layout_alignParentTop="true"
                        android:contentDescription="getApplicationFeatureShare#3"
                        android:orientation="vertical">
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:contentDescription="getApplicationFeatureShare"
                            android:layout_alignParentTop="true"
                            android:background="#E5E5E5"/>
                        <TextView
                            android:id="@+id/mbridge_app_desc_tv"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentLeft="true"
                            android:layout_centerVertical="true"
                            android:contentDescription="appdesc"
                            android:textSize="12sp"
                            android:textColor="#999999"
                            android:text="功能介绍"/>

                        <ImageView
                            android:id="@+id/mbridge_appdescrightname"
                            android:layout_width="15dp"
                            android:layout_height="15dp"
                            android:layout_alignParentRight="true"
                            android:layout_centerVertical="true"
                            android:contentDescription="appdescexpand"
                        />
                    </RelativeLayout>


                    <LinearLayout
                        android:id="@+id/mbridge_linear_share"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:visibility="gone"
                        android:contentDescription="appdesc"
                        android:orientation="vertical">
                        <TextView
                            android:id="@+id/mbridge_app_feature_share"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="12sp"
                            android:textColor="#808080"
                            android:contentDescription="getApplicationFeatureShare"/>
                    </LinearLayout>
                    </LinearLayout>
            </RelativeLayout>
        </ScrollView>
    </RelativeLayout>
</RelativeLayout>
