{"permissions": {"allow": ["WebFetch(domain:www.4kvm.tv)", "Bash(python3 -c \"\nimport requests\nimport sys\nsys.path.append(''.'')\nfrom base.spider import Spider\n\n# 创建Spider实例来使用其fetch方法\nsp = Spider()\n\nheaders = {\n    ''User-Agent'': ''Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'',\n    ''Accept'': ''text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'',\n    ''Accept-Language'': ''zh-CN,zh;q=0.9,en;q=0.8'',\n    ''Accept-Encoding'': ''gzip, deflate'',\n    ''Connection'': ''keep-alive''\n}\n\ntry:\n    print(''正在访问 https://www.4kvm.tv/ ...'')\n    resp = sp.fetch(''https://www.4kvm.tv/'', headers=headers)\n    print(f''响应状态码: {resp.status_code}'')\n    print(f''响应头: {dict(resp.headers)}'')\n    if resp.status_code == 200:\n        print(''=== 网站首页HTML结构 ==='')\n        print(resp.text[:2000])  # 只打印前2000字符\n        print(''...'')\n    else:\n        print(f''访问失败，状态码: {resp.status_code}'')\n        print(f''响应内容: {resp.text[:500]}'')\nexcept Exception as e:\n    print(f''访问网站失败: {e}'')\n\")", "Bash(python3 -c \"\nimport requests\n\nheaders = {\n    ''User-Agent'': ''Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'',\n    ''Accept'': ''text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'',\n    ''Accept-Language'': ''zh-CN,zh;q=0.9,en;q=0.8'',\n    ''Accept-Encoding'': ''gzip, deflate'',\n    ''Connection'': ''keep-alive''\n}\n\ntry:\n    print(''正在访问 https://www.4kvm.tv/ ...'')\n    resp = requests.get(''https://www.4kvm.tv/'', headers=headers, timeout=10)\n    print(f''响应状态码: {resp.status_code}'')\n    print(f''响应头: {dict(resp.headers)}'')\n    if resp.status_code == 200:\n        print(''=== 网站首页HTML结构 ==='')\n        print(resp.text[:3000])  # 打印前3000字符\n        print(''...'')\n    else:\n        print(f''访问失败，状态码: {resp.status_code}'')\n        print(f''响应内容: {resp.text[:500]}'')\nexcept Exception as e:\n    print(f''访问网站失败: {e}'')\n\")", "Bash(python3 -c \"\nimport requests\nimport urllib3\nurllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)\n\nheaders = {\n    ''User-Agent'': ''Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'',\n    ''Accept'': ''text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'',\n    ''Accept-Language'': ''zh-CN,zh;q=0.9,en;q=0.8'',\n    ''Connection'': ''keep-alive''\n}\n\ntry:\n    print(''正在访问 https://www.4kvm.tv/ (忽略SSL验证)...'')\n    resp = requests.get(''https://www.4kvm.tv/'', headers=headers, timeout=15, verify=False)\n    print(f''响应状态码: {resp.status_code}'')\n    if resp.status_code == 200:\n        print(''=== 网站首页HTML结构 ==='')\n        print(resp.text[:3000])\n    else:\n        print(f''访问失败，状态码: {resp.status_code}'')\n        print(f''响应内容: {resp.text[:500]}'')\nexcept Exception as e:\n    print(f''访问网站失败: {e}'')\n\")", "Bash(python3 -c \"\nimport requests\nimport urllib3\nurllib3.disable_warnings()\n\nheaders = {\n    ''User-Agent'': ''Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'',\n    ''Accept'': ''text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'',\n    ''Accept-Language'': ''zh-CN,zh;q=0.9'',\n    ''Cache-Control'': ''no-cache''\n}\n\n# 尝试多个可能的URL\nurls = [\n    ''http://www.4kvm.tv/'',\n    ''https://4kvm.tv/'',\n    ''http://4kvm.tv/'',\n    ''https://www.4kvm.site/'',\n    ''http://www.4kvm.site/''\n]\n\nfor url in urls:\n    try:\n        print(f''正在尝试访问: {url}'')\n        resp = requests.get(url, headers=headers, timeout=10, verify=False, allow_redirects=True)\n        print(f''状态码: {resp.status_code}'')\n        if resp.status_code == 200:\n            print(''=== 成功访问，HTML结构如下 ==='')\n            print(resp.text[:2000])\n            break\n        elif resp.status_code in [301, 302]:\n            print(f''重定向到: {resp.headers.get(\"\"Location\"\", \"\"未知\"\")}'')\n    except Exception as e:\n        print(f''访问失败: {e}'')\n    print()\n\")", "Bash(python3 -c \"\nimport requests\nimport urllib3\nfrom lxml import etree\nurllib3.disable_warnings()\n\nheaders = {\n    ''User-Agent'': ''Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'',\n    ''Accept'': ''text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'',\n    ''Accept-Language'': ''zh-CN,zh;q=0.9''\n}\n\ntry:\n    resp = requests.get(''http://www.4kvm.tv/'', headers=headers, timeout=10, verify=False)\n    html = etree.HTML(resp.text)\n    \n    print(''=== 网站导航菜单分析 ==='')\n    nav_links = html.xpath(''//nav//a | //ul[contains(@class,\"\"menu\"\")]//a | //div[contains(@class,\"\"nav\"\")]//a'')\n    for link in nav_links[:10]:\n        href = link.get(''href'', '''')\n        text = ''''.join(link.itertext()).strip()\n        if text and href:\n            print(f''菜单项: {text} -> {href}'')\n    \n    print(''\\n=== 视频内容区域分析 ==='')\n    video_containers = html.xpath(''//div[contains(@class,\"\"post\"\") or contains(@class,\"\"movie\"\") or contains(@class,\"\"video\"\") or contains(@class,\"\"item\"\")]'')\n    print(f''找到 {len(video_containers)} 个潜在视频容器'')\n    \n    if video_containers:\n        first_container = video_containers[0]\n        print(''\\n第一个视频容器的HTML结构:'')\n        print(etree.tostring(first_container, encoding=''unicode'', pretty_print=True)[:1000])\n    \n    print(''\\n=== 页面主要标签分析 ==='')\n    titles = html.xpath(''//h1 | //h2 | //h3'')\n    for title in titles[:5]:\n        text = ''''.join(title.itertext()).strip()\n        if text:\n            print(f''标题: {text}'')\n            \nexcept Exception as e:\n    print(f''分析失败: {e}'')\n\")", "Bash(python3 -c \"\nimport re\n\n# 读取网页文件\nwith open(''/tmp/4kvm_home.html'', ''r'', encoding=''utf-8'') as f:\n    content = f.read()\n\n# 查找导航菜单部分\nnav_pattern = r''<nav[^>]*>.*?</nav>''\nnav_matches = re.findall(nav_pattern, content, re.DOTALL)\nprint(''=== 导航菜单结构 ==='')\nfor i, nav in enumerate(nav_matches[:2]):\n    print(f''导航 {i+1}: {nav[:500]}...'')\n    print()\n\n# 查找主要内容区域\nmain_pattern = r''<main[^>]*>.*?</main>''\nmain_matches = re.findall(main_pattern, content, re.DOTALL)\nif main_matches:\n    print(''=== 主要内容区域 ==='')\n    print(main_matches[0][:1000] + ''...'')\n    print()\n\n# 查找视频/电影条目\nitem_patterns = [\n    r''<article[^>]*class=\"\"[^\"\"]*post[^\"\"]*\"\"[^>]*>.*?</article>'',\n    r''<div[^>]*class=\"\"[^\"\"]*item[^\"\"]*\"\"[^>]*>.*?</div>'',\n    r''<div[^>]*class=\"\"[^\"\"]*movie[^\"\"]*\"\"[^>]*>.*?</div>''\n]\n\nprint(''=== 视频条目结构 ==='')\nfor pattern in item_patterns:\n    matches = re.findall(pattern, content, re.DOTALL)\n    if matches:\n        print(f''找到 {len(matches)} 个匹配项目'')\n        print(''第一个项目:'')\n        print(matches[0][:800] + ''...'')\n        break\n\")", "Bash(python3 -c \"\nimport re\n\n# 读取网页文件\nwith open(''/tmp/4kvm_home.html'', ''r'', encoding=''utf-8'') as f:\n    content = f.read()\n\n# 查找主要内容区域\nprint(''=== 寻找主要内容区域 ==='')\n# 查找包含视频内容的容器\ncontainer_patterns = [\n    r''<div[^>]*class=\"\"[^\"\"]*contenedor[^\"\"]*\"\"[^>]*>'',\n    r''<div[^>]*id=\"\"[^\"\"]*contenedor[^\"\"]*\"\"[^>]*>'',\n    r''<main[^>]*>'',\n    r''<div[^>]*class=\"\"[^\"\"]*content[^\"\"]*\"\"[^>]*>'',\n    r''<div[^>]*class=\"\"[^\"\"]*movies[^\"\"]*\"\"[^>]*>''\n]\n\nfor pattern in container_patterns:\n    matches = re.findall(pattern, content)\n    if matches:\n        print(f''找到容器: {matches[0]}'')\n\n# 查找具体的视频/电影项目\nprint(''\\n=== 视频项目结构分析 ==='')\n# 查找链接模式\nlink_patterns = [\n    r''<a[^>]*href=\"\"[^\"\"]*\\/tvshows\\/[^\"\"]*\"\"[^>]*>.*?</a>'',\n    r''<a[^>]*href=\"\"[^\"\"]*\\/movies\\/[^\"\"]*\"\"[^>]*>.*?</a>'',\n    r''<a[^>]*href=\"\"[^\"\"]*\\/[^\"\"]*\"\"[^>]*title=\"\"[^\"\"]*\"\"[^>]*>.*?</a>''\n]\n\nfor i, pattern in enumerate(link_patterns):\n    matches = re.findall(pattern, content, re.DOTALL)\n    if matches:\n        print(f''\\n链接模式 {i+1}: 找到 {len(matches)} 个链接'')\n        for j, match in enumerate(matches[:3]):\n            print(f''  链接 {j+1}: {match[:200]}...'')\n\n# 查找图片和标题\nprint(''\\n=== 图片和标题结构 ==='')\nimg_patterns = [\n    r''<img[^>]*src=\"\"[^\"\"]*\"\"[^>]*alt=\"\"[^\"\"]*\"\"[^>]*>'',\n    r''<img[^>]*class=\"\"[^\"\"]*\"\"[^>]*src=\"\"[^\"\"]*\"\"[^>]*>''\n]\n\nfor pattern in img_patterns:\n    matches = re.findall(pattern, content)\n    if matches:\n        print(f''图片格式: {matches[0]}'')\n        break\n\")", "Bash(python3 plugin/html/4kvm.py)", "Bash(python3 4kvm.py)", "Bash(PYTHONPATH=../.. python3 4kvm.py)"], "deny": []}}