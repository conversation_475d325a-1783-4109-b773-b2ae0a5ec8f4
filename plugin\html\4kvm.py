# -*- coding: utf-8 -*-
# 4kvm.tv 高清影视 PyramidStore插件
# 基于DooPlay WordPress主题的影视网站
import sys
sys.path.append('..')
from base.spider import Spider
import json
import re
from urllib.parse import urljoin, quote

class Spider(Spider):
    def init(self, extend=""):
        """初始化插件"""
        # 使用HTTP协议，避免SSL问题
        self.host = "http://www.4kvm.tv"
        
        # 标准浏览器请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
            'Referer': self.host
        }
        
    def getName(self):
        return "4kvm高清影视"

    def homeContent(self, filter=None):
        """首页内容 - 获取分类信息和推荐内容"""
        try:
            result = {
                'class': [],
                'list': []
            }
            
            # 尝试获取首页分析真实分类结构
            resp = self.fetch(self.host, headers=self.headers, verify=False)
            if resp and resp.status_code == 200:
                html = self.html(resp.text)
                
                # 获取导航菜单中的分类
                nav_links = html.xpath('//nav//a | //ul[contains(@class,"menu")]//a | //div[contains(@class,"nav")]//a')
                
                categories = []
                for link in nav_links:
                    href = link.get('href', '')
                    text = ''.join(link.itertext()).strip()
                    
                    if text and href and any(keyword in href.lower() for keyword in ['movies', 'tvshows', 'genre', 'category']):
                        # 提取分类ID
                        if '/movies' in href:
                            categories.append({'type_id': 'movies', 'type_name': '电影'})
                        elif '/tvshows' in href:
                            categories.append({'type_id': 'tvshows', 'type_name': '电视剧'})
                        elif '/genre/' in href:
                            genre_id = self.regStr(r'/genre/([^/]+)', href)
                            if genre_id:
                                categories.append({'type_id': f'genre_{genre_id}', 'type_name': text})
                
                # 如果没有找到分类，使用默认分类
                if not categories:
                    categories = [
                        {'type_id': 'movies', 'type_name': '电影'},
                        {'type_id': 'tvshows', 'type_name': '电视剧'},
                        {'type_id': 'genre_action', 'type_name': '动作'},
                        {'type_id': 'genre_drama', 'type_name': '剧情'},
                        {'type_id': 'genre_comedy', 'type_name': '喜剧'},
                        {'type_id': 'genre_thriller', 'type_name': '惊悚'},
                        {'type_id': 'genre_romance', 'type_name': '爱情'},
                        {'type_id': 'genre_horror', 'type_name': '恐怖'}
                    ]
                
                result['class'] = categories
                
                # 获取首页推荐视频 - 基于真实HTML结构
                # DooPlay主题的视频条目结构
                video_items = html.xpath('//article[contains(@class,"item")]')
                if not video_items:
                    # 备用选择器
                    video_items = html.xpath('//div[contains(@class,"poster")]')
                
                for item in video_items[:20]:  # 限制20个
                    try:
                        # 获取链接
                        link = item.xpath('.//a/@href')[0] if item.xpath('.//a/@href') else ''
                        if not link:
                            continue
                            
                        # 获取标题
                        title_selectors = [
                            './/h3[@class="title"]//text()',
                            './/h2[@class="title"]//text()',
                            './/a/@title',
                            './/img/@alt'
                        ]
                        
                        title = ''
                        for selector in title_selectors:
                            title_results = item.xpath(selector)
                            if title_results:
                                title = ''.join(title_results).strip()
                                if title:
                                    break
                        
                        if not title:
                            continue
                            
                        # 获取封面图
                        img_selectors = [
                            './/img/@src',
                            './/img/@data-src'
                        ]
                        
                        pic = ''
                        for selector in img_selectors:
                            img_results = item.xpath(selector)
                            if img_results:
                                pic = img_results[0]
                                if not pic.startswith('http'):
                                    pic = urljoin(self.host, pic)
                                break
                        
                        # 获取其他信息
                        year = ''
                        year_elem = item.xpath('.//span[contains(@class,"year")]//text()')
                        if year_elem:
                            year = year_elem[0].strip()
                        
                        remarks = ''
                        quality_elem = item.xpath('.//span[contains(@class,"quality")]//text()')
                        if quality_elem:
                            remarks = quality_elem[0].strip()
                        
                        # 提取视频ID
                        vid = link.split('/')[-1] if link else ''
                        
                        result['list'].append({
                            'vod_id': vid,
                            'vod_name': title,
                            'vod_pic': pic,
                            'vod_remarks': remarks,
                            'vod_year': year,
                            'vod_area': '',
                            'vod_director': '',
                            'vod_actor': '',
                            'vod_content': ''
                        })
                        
                    except Exception as e:
                        self.log(f"处理视频条目失败: {e}")
                        continue
            
            return result
            
        except Exception as e:
            self.log(f"homeContent error: {e}")
            return {'class': [], 'list': []}

    def categoryContent(self, tid, pg, filter=None, extend=None):
        """分类内容 - 基于真实URL结构"""
        try:
            page = int(pg) if pg else 1
            result = {
                'list': [],
                'page': page,
                'pagecount': 1,
                'limit': 20,
                'total': 0
            }
            
            # 构建分类URL - 基于4kvm.tv的真实URL结构
            if tid == 'movies':
                url = f"{self.host}/movies"
            elif tid == 'tvshows':
                url = f"{self.host}/tvshows"
            elif tid.startswith('genre_'):
                genre = tid.replace('genre_', '')
                url = f"{self.host}/genre/{genre}"
            else:
                # 其他分类
                url = f"{self.host}/{tid}"
                
            # 添加分页参数
            if page > 1:
                url += f"/page/{page}"
                
            resp = self.fetch(url, headers=self.headers, verify=False)
            
            if resp and resp.status_code == 200:
                html = self.html(resp.text)
                
                # 基于DooPlay主题的视频列表结构
                video_items = html.xpath('//article[contains(@class,"item")]')
                if not video_items:
                    # 备用选择器，适应不同页面结构
                    video_items = html.xpath('//div[contains(@class,"poster")] | //div[contains(@class,"movie-item")]')
                
                for item in video_items:
                    try:
                        # 获取链接
                        link_elem = item.xpath('.//a[@href]')
                        if not link_elem:
                            continue
                        link = link_elem[0].get('href', '')
                        
                        if not link.startswith('http'):
                            link = urljoin(self.host, link)
                        
                        # 获取标题
                        title = ''
                        title_selectors = [
                            './/h3[contains(@class,"title")]//text()',
                            './/h2[contains(@class,"title")]//text()', 
                            './/a/@title',
                            './/img/@alt'
                        ]
                        
                        for selector in title_selectors:
                            title_results = item.xpath(selector)
                            if title_results:
                                title = ''.join(title_results).strip()
                                if title:
                                    break
                        
                        if not title:
                            continue
                            
                        # 获取封面图
                        pic = ''
                        img_selectors = [
                            './/img/@src',
                            './/img/@data-src',
                            './/img/@data-lazy'
                        ]
                        
                        for selector in img_selectors:
                            img_results = item.xpath(selector)
                            if img_results:
                                pic = img_results[0]
                                if pic and not pic.startswith('http'):
                                    pic = urljoin(self.host, pic)
                                if pic:
                                    break
                        
                        # 获取年份
                        year = ''
                        year_selectors = [
                            './/span[contains(@class,"year")]//text()',
                            './/span[contains(@class,"date")]//text()',
                            './/time//text()'
                        ]
                        
                        for selector in year_selectors:
                            year_results = item.xpath(selector)
                            if year_results:
                                year_text = ''.join(year_results).strip()
                                # 提取年份
                                year_match = re.search(r'\b(19|20)\d{2}\b', year_text)
                                if year_match:
                                    year = year_match.group()
                                    break
                        
                        # 获取备注信息
                        remarks = ''
                        remark_selectors = [
                            './/span[contains(@class,"quality")]//text()',
                            './/span[contains(@class,"episode")]//text()',
                            './/span[contains(@class,"status")]//text()'
                        ]
                        
                        for selector in remark_selectors:
                            remark_results = item.xpath(selector)
                            if remark_results:
                                remarks = ''.join(remark_results).strip()
                                if remarks:
                                    break
                        
                        # 获取简短描述
                        content = ''
                        content_selectors = [
                            './/p[contains(@class,"description")]//text()',
                            './/div[contains(@class,"summary")]//text()'
                        ]
                        
                        for selector in content_selectors:
                            content_results = item.xpath(selector)
                            if content_results:
                                content = ''.join(content_results).strip()[:100]
                                if content:
                                    break
                        
                        # 提取视频ID
                        vid = link.split('/')[-1] if link else ''
                        
                        result['list'].append({
                            'vod_id': vid,
                            'vod_name': title,
                            'vod_pic': pic,
                            'vod_remarks': remarks,
                            'vod_year': year,
                            'vod_area': '',
                            'vod_actor': '',
                            'vod_director': '',
                            'vod_content': content
                        })
                        
                    except Exception as e:
                        self.log(f"处理视频条目失败: {e}")
                        continue
                
                # 获取分页信息
                try:
                    # DooPlay主题的分页结构
                    pagination = html.xpath('//div[contains(@class,"pagination")]//a | //nav[contains(@class,"navigation")]//a')
                    if pagination:
                        page_numbers = []
                        for page_link in pagination:
                            page_text = ''.join(page_link.itertext()).strip()
                            if page_text.isdigit():
                                page_numbers.append(int(page_text))
                        
                        if page_numbers:
                            result['pagecount'] = max(page_numbers)
                        
                        # 估算总数
                        result['total'] = result['pagecount'] * result['limit']
                except Exception as e:
                    self.log(f"获取分页信息失败: {e}")
                    
            return result
            
        except Exception as e:
            self.log(f"categoryContent error: {e}")
            return {'list': [], 'page': 1, 'pagecount': 1, 'limit': 20, 'total': 0}

    def detailContent(self, ids):
        """详情内容 - 基于DooPlay主题结构"""
        try:
            vid = ids[0] if isinstance(ids, list) else ids
            
            # 构建详情页URL - 基于真实的4kvm.tv URL结构
            # 从首页分析可知，URL格式如：/tvshows/lijimg, /movies/nazhnaohai
            detail_url = f"{self.host}/{vid}" if vid.count('/') > 0 else f"{self.host}/movies/{vid}"
            
            resp = self.fetch(detail_url, headers=self.headers, verify=False)
            
            result = {'list': []}
            
            if resp and resp.status_code == 200:
                html = self.html(resp.text)
                
                # 获取标题 - DooPlay主题结构
                title = ''
                title_selectors = [
                    '//h1[contains(@class,"title")]//text()',
                    '//h1//text()',
                    '//h2[contains(@class,"title")]//text()',
                    '//title//text()'
                ]
                
                for selector in title_selectors:
                    try:
                        titles = html.xpath(selector)
                        if titles:
                            title = ''.join(titles).strip()
                            # 清理标题中的网站名
                            title = re.sub(r'\s*-\s*4k影视.*$', '', title)
                            title = re.sub(r'^《(.*)》.*$', r'\1', title)
                            if title:
                                break
                    except:
                        continue
                
                # 获取封面图
                pic = ''
                pic_selectors = [
                    '//div[contains(@class,"poster")]//img/@src',
                    '//div[contains(@class,"sheader")]//img/@src',
                    '//img[contains(@class,"poster")]/@src',
                    '//meta[@property="og:image"]/@content'
                ]
                
                for selector in pic_selectors:
                    try:
                        pics = html.xpath(selector)
                        if pics:
                            pic = pics[0]
                            if pic and not pic.startswith('http'):
                                pic = urljoin(self.host, pic)
                            if pic:
                                break
                    except:
                        continue
                
                # 获取详细信息
                info_dict = {}
                
                # 获取年份
                year_selectors = [
                    '//span[contains(@class,"year")]//text()',
                    '//time//text()',
                    '//div[contains(@class,"meta")]//span[contains(text(),"年")]//text()'
                ]
                
                for selector in year_selectors:
                    try:
                        year_results = html.xpath(selector)
                        if year_results:
                            year_text = ''.join(year_results)
                            year_match = re.search(r'\b(19|20)\d{2}\b', year_text)
                            if year_match:
                                info_dict['vod_year'] = year_match.group()
                                break
                    except:
                        continue
                
                # 获取地区
                area_selectors = [
                    '//span[contains(@class,"country")]//text()',
                    '//div[contains(@class,"meta")]//span[contains(text(),"地区")]//following-sibling::text()',
                    '//div[contains(@class,"sgeneros")]//a//text()'
                ]
                
                for selector in area_selectors:
                    try:
                        area_results = html.xpath(selector)
                        if area_results:
                            info_dict['vod_area'] = ''.join(area_results).strip()
                            break
                    except:
                        continue
                
                # 获取导演
                director_selectors = [
                    '//div[contains(@class,"director")]//a//text()',
                    '//span[contains(@class,"director")]//text()',
                    '//div[contains(@class,"meta")]//span[contains(text(),"导演")]//following-sibling::a//text()'
                ]
                
                for selector in director_selectors:
                    try:
                        director_results = html.xpath(selector)
                        if director_results:
                            info_dict['vod_director'] = ', '.join(director_results)
                            break
                    except:
                        continue
                
                # 获取演员
                actor_selectors = [
                    '//div[contains(@class,"cast")]//a//text()',
                    '//span[contains(@class,"actor")]//text()',
                    '//div[contains(@class,"meta")]//span[contains(text(),"主演")]//following-sibling::a//text()'
                ]
                
                for selector in actor_selectors:
                    try:
                        actor_results = html.xpath(selector)
                        if actor_results:
                            info_dict['vod_actor'] = ', '.join(actor_results[:5])  # 限制演员数量
                            break
                    except:
                        continue
                
                # 获取类型
                type_selectors = [
                    '//div[contains(@class,"genres")]//a//text()',
                    '//span[contains(@class,"genre")]//text()',
                    '//div[contains(@class,"sgeneros")]//a//text()'
                ]
                
                type_name = ''
                for selector in type_selectors:
                    try:
                        type_results = html.xpath(selector)
                        if type_results:
                            type_name = ', '.join(type_results[:3])
                            break
                    except:
                        continue
                
                # 获取简介
                content = ''
                content_selectors = [
                    '//div[contains(@class,"wp-content")]//p//text()',
                    '//div[contains(@class,"description")]//text()',
                    '//div[contains(@class,"plot")]//text()',
                    '//meta[@name="description"]/@content'
                ]
                
                for selector in content_selectors:
                    try:
                        content_results = html.xpath(selector)
                        if content_results:
                            content = ''.join(content_results).strip()
                            if len(content) > 50:  # 确保内容有意义
                                break
                    except:
                        continue
                
                # 获取播放链接 - DooPlay主题结构
                vod_play_from = []
                vod_play_url = []
                
                # 查找播放器数据
                try:
                    # 查找iframe或播放器容器
                    player_selectors = [
                        '//div[contains(@class,"dooplay_player")]',
                        '//div[contains(@class,"player")]',
                        '//iframe[contains(@src,"player")]'
                    ]
                    
                    # 查找剧集列表
                    episode_selectors = [
                        '//ul[contains(@class,"episodios")]//li',
                        '//div[contains(@class,"se-c")]//ul//li',
                        '//div[contains(@class,"episodes")]//a'
                    ]
                    
                    episodes = []
                    for selector in episode_selectors:
                        try:
                            episode_elements = html.xpath(selector)
                            if episode_elements:
                                episodes = episode_elements
                                break
                        except:
                            continue
                    
                    if episodes:
                        vod_play_from.append("默认线路")
                        episode_list = []
                        
                        for i, ep in enumerate(episodes[:50]):  # 限制集数
                            try:
                                # 获取剧集标题
                                ep_title_selectors = [
                                    './/a//text()',
                                    './/span//text()',
                                    './/@title'
                                ]
                                
                                ep_title = ''
                                for ts in ep_title_selectors:
                                    try:
                                        title_results = ep.xpath(ts)
                                        if title_results:
                                            ep_title = ''.join(title_results).strip()
                                            if ep_title:
                                                break
                                    except:
                                        continue
                                
                                if not ep_title:
                                    ep_title = f"第{i+1}集"
                                
                                # 获取播放链接
                                ep_href_selectors = [
                                    './/a/@href',
                                    './/@data-link',
                                    './/@data-url'
                                ]
                                
                                ep_href = ''
                                for hs in ep_href_selectors:
                                    try:
                                        href_results = ep.xpath(hs)
                                        if href_results:
                                            ep_href = href_results[0]
                                            break
                                    except:
                                        continue
                                
                                if ep_href:
                                    if not ep_href.startswith('http'):
                                        ep_href = urljoin(self.host, ep_href)
                                    episode_list.append(f"{ep_title}${ep_href}")
                            except:
                                continue
                        
                        if episode_list:
                            vod_play_url.append('#'.join(episode_list))
                    else:
                        # 单集内容，查找直接播放链接
                        vod_play_from.append("在线播放")
                        vod_play_url.append(f"播放${detail_url}")
                        
                except Exception as e:
                    self.log(f"获取播放链接失败: {e}")
                    # 默认播放链接
                    vod_play_from.append("在线播放")
                    vod_play_url.append(f"播放${detail_url}")
                
                result['list'].append({
                    'vod_id': vid,
                    'vod_name': title,
                    'vod_pic': pic,
                    'type_name': type_name,
                    'vod_year': info_dict.get('vod_year', ''),
                    'vod_area': info_dict.get('vod_area', ''),
                    'vod_remarks': '',
                    'vod_actor': info_dict.get('vod_actor', ''),
                    'vod_director': info_dict.get('vod_director', ''),
                    'vod_content': content,
                    'vod_play_from': '$$$'.join(vod_play_from),
                    'vod_play_url': '$$$'.join(vod_play_url)
                })
                
            return result
            
        except Exception as e:
            self.log(f"detailContent error: {e}")
            return {'list': []}

    def searchContent(self, key, quick=None):
        """搜索内容 - 基于4kvm.tv的搜索功能"""
        try:
            # 基于首页分析，搜索表单的action是xssearch
            search_url = f"{self.host}/xssearch"
            
            # 构建搜索参数
            params = {'s': key}
            
            resp = self.fetch(search_url, params=params, headers=self.headers, verify=False)
            
            result = {'list': []}
            
            if resp and resp.status_code == 200:
                html = self.html(resp.text)
                
                # 基于DooPlay主题的搜索结果结构
                search_items = html.xpath('//article[contains(@class,"item")] | //div[contains(@class,"result")] | //div[contains(@class,"search-item")]')
                
                for item in search_items:
                    try:
                        # 获取链接
                        link_elem = item.xpath('.//a[@href]')
                        if not link_elem:
                            continue
                        link = link_elem[0].get('href', '')
                        
                        if not link.startswith('http'):
                            link = urljoin(self.host, link)
                        
                        # 获取标题
                        title = ''
                        title_selectors = [
                            './/h3[contains(@class,"title")]//text()',
                            './/h2[contains(@class,"title")]//text()',
                            './/a/@title',
                            './/img/@alt'
                        ]
                        
                        for selector in title_selectors:
                            title_results = item.xpath(selector)
                            if title_results:
                                title = ''.join(title_results).strip()
                                if title:
                                    break
                        
                        if not title:
                            continue
                            
                        # 获取封面图
                        pic = ''
                        img_selectors = [
                            './/img/@src',
                            './/img/@data-src'
                        ]
                        
                        for selector in img_selectors:
                            img_results = item.xpath(selector)
                            if img_results:
                                pic = img_results[0]
                                if pic and not pic.startswith('http'):
                                    pic = urljoin(self.host, pic)
                                if pic:
                                    break
                        
                        # 获取年份和类型
                        year = ''
                        year_elem = item.xpath('.//span[contains(@class,"year")]//text() | .//time//text()')
                        if year_elem:
                            year_text = ''.join(year_elem)
                            year_match = re.search(r'\b(19|20)\d{2}\b', year_text)
                            if year_match:
                                year = year_match.group()
                        
                        # 获取备注信息
                        remarks = ''
                        remark_elem = item.xpath('.//span[contains(@class,"quality")]//text() | .//span[contains(@class,"type")]//text()')
                        if remark_elem:
                            remarks = ''.join(remark_elem).strip()
                        
                        # 获取简短描述
                        content = ''
                        content_elem = item.xpath('.//p[contains(@class,"description")]//text() | .//div[contains(@class,"summary")]//text()')
                        if content_elem:
                            content = ''.join(content_elem).strip()[:100]
                        
                        # 提取视频ID
                        vid = link.split('/')[-1] if link else ''
                        
                        result['list'].append({
                            'vod_id': vid,
                            'vod_name': title,
                            'vod_pic': pic,
                            'vod_remarks': remarks,
                            'vod_year': year,
                            'vod_area': '',
                            'vod_actor': '',
                            'vod_director': '',
                            'vod_content': content
                        })
                        
                    except Exception as e:
                        self.log(f"处理搜索结果失败: {e}")
                        continue
                        
            return result
            
        except Exception as e:
            self.log(f"searchContent error: {e}")
            return {'list': []}

    def playerContent(self, flag=None, id=None, vipFlags=None):
        """播放内容 - 解析4kvm.tv的播放链接"""
        try:
            result = {
                'parse': 0,
                'playUrl': '',
                'url': id,
                'header': self.headers
            }
            
            if id and id.startswith('http'):
                # 如果是播放页面URL，需要解析获取真实视频链接
                resp = self.fetch(id, headers=self.headers, verify=False)
                
                if resp and resp.status_code == 200:
                    # 尝试从页面中提取视频链接
                    # DooPlay主题可能使用iframe嵌入播放器
                    
                    # 查找iframe播放器
                    iframe_pattern = r'<iframe[^>]*src="([^"]+)"[^>]*>'
                    iframe_matches = re.findall(iframe_pattern, resp.text)
                    
                    if iframe_matches:
                        for iframe_url in iframe_matches:
                            if any(keyword in iframe_url.lower() for keyword in ['player', 'play', 'video']):
                                result['url'] = iframe_url
                                result['playUrl'] = iframe_url
                                result['parse'] = 1  # 需要解析iframe
                                break
                    
                    # 查找直接的视频链接
                    video_url_patterns = [
                        r'"url"\s*:\s*"([^"]+)"',
                        r"'url'\s*:\s*'([^']+)'",
                        r'src\s*:\s*"([^"]+\.(?:mp4|m3u8|flv))"',
                        r'https?://[^"\'\\s]+\.(?:mp4|m3u8|flv)[^"\'\\s]*',
                        r'player_data[^}]*url[^"]*"([^"]+)"'
                    ]
                    
                    for pattern in video_url_patterns:
                        matches = re.findall(pattern, resp.text, re.IGNORECASE)
                        if matches:
                            video_url = matches[0]
                            if video_url.startswith('http'):
                                result['url'] = video_url
                                result['playUrl'] = video_url
                                result['parse'] = 0
                                break
                    
                    # 查找DooPlay主题特有的播放器配置
                    dooplay_patterns = [
                        r'var\s+video_player\s*=\s*"([^"]+)"',
                        r'dooplayer\([^)]*"([^"]+)"',
                        r'player_ajax[^}]*url[^"]*"([^"]+)"'
                    ]
                    
                    for pattern in dooplay_patterns:
                        matches = re.findall(pattern, resp.text)
                        if matches:
                            player_url = matches[0]
                            if player_url.startswith('http'):
                                result['url'] = player_url
                                result['playUrl'] = player_url
                                break
                
                # 添加必要的请求头
                result['header'].update({
                    'Referer': id,
                    'Origin': self.host
                })
                
            return result
            
        except Exception as e:
            self.log(f"playerContent error: {e}")
            return {'parse': 0, 'playUrl': '', 'url': '', 'header': {}}

    def isVideoFormat(self, url):
        pass

    def manualVideoCheck(self):
        pass

    def destroy(self):
        pass

    def localProxy(self, param):
        pass


if __name__ == "__main__":
    sp = Spider()
    sp.init()
    
    print("=== 测试首页内容 ===")
    home_result = sp.homeContent(False)
    print(json.dumps(home_result, ensure_ascii=False, indent=2))
    
    print("\n=== 测试分类内容 ===")
    category_result = sp.categoryContent('movies', '1', False, {})
    print(json.dumps(category_result, ensure_ascii=False, indent=2))
    
    print("\n=== 测试搜索功能 ===")
    search_result = sp.searchContent('利剑', False)
    print(json.dumps(search_result, ensure_ascii=False, indent=2))
    
    # 如果有结果，测试详情页
    if home_result.get('list'):
        first_video = home_result['list'][0]
        print(f"\n=== 测试详情页 ({first_video['vod_name']}) ===")
        detail_result = sp.detailContent([first_video['vod_id']])
        print(json.dumps(detail_result, ensure_ascii=False, indent=2))