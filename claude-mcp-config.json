{"mcpServers": {"crawl4ai": {"command": "python", "args": ["-m", "crawl4ai_mcp_server"], "env": {"CRAWL4AI_API_KEY": ""}}, "context7": {"command": "python", "args": ["-m", "context7_mcp_server"], "env": {"CONTEXT7_API_KEY": ""}}, "playwright": {"command": "python", "args": ["-m", "playwright_mcp_server"], "env": {"PLAYWRIGHT_HEADLESS": "true"}}, "edgeone-pages": {"command": "python", "args": ["-m", "edgeone_pages_mcp_server"], "env": {"EDGEONE_API_KEY": "", "EDGEONE_SECRET_KEY": ""}}, "sequential-thinking": {"command": "python", "args": ["-m", "sequential_thinking_mcp_server"]}, "filesystem": {"command": "npx", "args": ["@modelcontextprotocol/server-filesystem", "d:\\augment-projects\\py2\\PyramidStore-18-main"], "env": {"ALLOWED_EXTENSIONS": ".py,.json,.md,.txt,.js,.html,.css,.yml,.yaml,.xml"}}, "str-replace-editor": {"command": "python", "args": ["-m", "str_replace_editor_mcp_server"]}, "web-search": {"command": "python", "args": ["-m", "web_search_mcp_server"], "env": {"GOOGLE_API_KEY": "", "GOOGLE_CSE_ID": ""}}, "open-browser": {"command": "python", "args": ["-m", "open_browser_mcp_server"]}, "memory": {"command": "python", "args": ["-m", "memory_mcp_server"]}, "mermaid": {"command": "python", "args": ["-m", "mermaid_mcp_server"]}, "diagnostics": {"command": "python", "args": ["-m", "diagnostics_mcp_server"]}, "terminal": {"command": "python", "args": ["-m", "terminal_mcp_server"]}, "process": {"command": "python", "args": ["-m", "process_mcp_server"]}, "git": {"command": "python", "args": ["-m", "git_mcp_server"], "env": {"GIT_REPOSITORY": "d:\\augment-projects\\py2\\PyramidStore-18-main"}}, "codebase-retrieval": {"command": "python", "args": ["-m", "codebase_retrieval_mcp_server"], "env": {"WORKSPACE_PATH": "d:\\augment-projects\\py2\\PyramidStore-18-main"}}, "web-fetch": {"command": "python", "args": ["-m", "web_fetch_mcp_server"]}}, "globalSettings": {"maxConcurrentServers": 10, "serverStartupTimeout": 15000, "logLevel": "info"}}